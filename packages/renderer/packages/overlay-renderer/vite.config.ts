import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import glsl from 'vite-plugin-glsl'

export const glslPlugin = glsl({
  include: [                      // Glob pattern, or array of glob patterns to import
    '**/*.glsl', '**/*.wgsl',
    '**/*.vert', '**/*.frag',
    '**/*.vs', '**/*.fs'
  ],
  exclude: undefined,             // Glob pattern, or array of glob patterns to ignore
  defaultExtension: 'glsl',       // Shader suffix to use when no extension is specified
  warnDuplicatedImports: true,    // Warn if the same chunk was imported multiple times
  removeDuplicatedImports: false, // Automatically remove an already imported chunk
  importKeyword: '#include',      // Keyword used to import shader chunks
  minify: false,                  // Minify/optimize output shader code
  watch: true,                    // Recompile shader on change
  root: '/'                       // Directory for root imports
})

export default defineConfig({
  plugins: [
    react(),
    glslPlugin,
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/entry'),
      name: 'OverlayRenderer',
      formats: ['es'],
      fileName: 'entry'
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'remotion',
        'opentype.js'
      ],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          remotion: 'Remotion'
        }
      }
    },
    sourcemap: false,
    emptyOutDir: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      '@app/shared': resolve(__dirname, '../../../shared'),
      '@clipnest/remotion-shared': resolve(__dirname, '../remotion-shared')
    }
  }
})
