import React, { useMemo } from 'react'
import { Audio, Easing, interpolate, useCurrentFrame } from 'remotion'
import { SoundOverlay } from '@app/shared/types/overlay'
import { useRenderContext } from '../../../render.context'
import { getProgressiveOverlayProps } from '../../../utils/getProgressiveOverlayProps'

interface SoundLayerContentProps {
  overlay: SoundOverlay
}

const useSoundLayerVolume = (overlay: SoundOverlay) => {
  const currentFrame = useCurrentFrame()
  const { playerMetadata: { fps } } = useRenderContext()

  return useMemo(
    () => {
      // 获取淡入淡出时长（秒转换为帧数）
      const { fadeInDuration = 0, fadeOutDuration = 0 } = overlay
      const fadeInFrames = fadeInDuration * fps
      const fadeOutFrames = fadeOutDuration * fps
      const totalFrames = overlay.durationInFrames

      let finalVolume = overlay.styles?.volume ?? 1

      // 计算淡入效果
      if (fadeInFrames > 0 && currentFrame < fadeInFrames) {
        const fadeInProgress = interpolate(
          currentFrame,
          [0, fadeInFrames],
          [0, 1],
          {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
            easing: Easing.out(Easing.cubic)
          }
        )
        finalVolume *= fadeInProgress
      }

      // 计算淡出效果
      if (fadeOutFrames > 0 && currentFrame > totalFrames - fadeOutFrames) {
        const fadeOutProgress = interpolate(
          currentFrame,
          [totalFrames - fadeOutFrames, totalFrames],
          [1, 0],
          {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
            easing: Easing.out(Easing.cubic)
          }
        )
        finalVolume *= fadeOutProgress
      }

      return finalVolume
    },
    [
      fps, currentFrame,
      overlay.styles?.volume, overlay.fadeInDuration, overlay.fadeOutDuration, overlay.durationInFrames
    ]
  )
}

export const SoundLayerContent: React.FC<SoundLayerContentProps> = ({ overlay }) => {
  const volume = useSoundLayerVolume(overlay)

  return (
    <Audio
      src={overlay.localSrc || overlay.src}
      volume={volume}
      {...getProgressiveOverlayProps(overlay)}
    />
  )
}
