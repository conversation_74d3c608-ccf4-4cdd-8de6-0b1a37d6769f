import React from 'react'
import { StickerOverlay } from '@app/shared/types/overlay'
import { useOverlayAnimation } from '../../../hooks'

interface StickerLayerContentProps {
  overlay: StickerOverlay
  onUpdate?: (updates: Partial<StickerOverlay>) => void
}

// 主组件
export const StickerLayerContent: React.FC<StickerLayerContentProps> = ({ overlay }) => {
  const animation = useOverlayAnimation(overlay)

  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectPosition: overlay.styles.objectPosition,
    opacity: overlay.styles.opacity,
    transform: overlay.styles.transform || 'none',
    filter: overlay.styles.filter || 'none',
    borderRadius: overlay.styles.borderRadius || '0px',
    boxShadow: overlay.styles.boxShadow || 'none',
    border: overlay.styles.border || 'none',
    ...animation,
  }

  /**
       * Create a container style that includes padding and background color
       */
  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    padding: overlay.styles.padding || '0px',
    backgroundColor: overlay.styles.paddingBackgroundColor || 'transparent',
    display: 'flex', // Use flexbox for centering
    alignItems: 'center',
    justifyContent: 'center',
  }

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '4px',
        opacity: overlay.styles?.opacity || 1,
        transform: `rotate(${overlay.rotation || 0}deg)`,
        ...containerStyle
      }}
    >
      <img
        src={overlay.localSrc || overlay.src}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          ...imageStyle,
        }}
        alt="贴纸"
      />
    </div>
  )
}
