import React, { useMemo } from 'react'
import { Overlay, OverlayType, RenderableOverlay } from '@app/shared/types/overlay'
import { Sequence } from 'remotion'
import { CaptionLayerContent, SoundLayerContent, StickerLayerContent, TextLayerContent } from './layers'

interface LayerContentProps {
  overlay: (
    | Overlay & { type: OverlayType.TEXT }
    | Overlay & { type: OverlayType.CAPTION }
    | Overlay & { type: OverlayType.STICKER }
  )
}

const COMMON_STYLE: React.CSSProperties = {
  width: '100%',
  height: '100%',
}

const SimpleLayerContent: React.FC<LayerContentProps> = ({ overlay }) => {
  switch (overlay.type) {
    case OverlayType.TEXT:
      return (
        <div style={{ ...COMMON_STYLE }}>
          <TextLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.CAPTION:
      return (
        <div
          style={{
            ...COMMON_STYLE,
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
          }}
        >
          <CaptionLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.STICKER:
      return (
        <div style={{ ...COMMON_STYLE }}>
          <StickerLayerContent overlay={overlay} />
        </div>
      )

    default:
      return null
  }
}

export const SimpleLayer: React.FC<{
  overlay: RenderableOverlay
}> = ({ overlay }) => {
  if (overlay.type === 'sound') {
    return (
      <Sequence
        key={overlay.id}
        from={overlay.from}
        durationInFrames={overlay.durationInFrames}
      >
        <SoundLayerContent overlay={overlay} />
      </Sequence>
    )
  }

  if (overlay.type !== OverlayType.TEXT && overlay.type !== OverlayType.CAPTION && overlay.type !== OverlayType.STICKER) {
    return null
  }

  /**
   * Memoized style calculations for the layer
   * Handles positioning, dimensions, rotation, and z-index based on:
   * - Overlay position (left, top)
   * - Dimensions (width, height)
   * - Rotation
   * - Row position for z-index stacking
   * - Selection state for pointer events
   *
   * @returns {React.CSSProperties} Computed styles for the layer
   */
  const style: React.CSSProperties = useMemo(() => {
    return {
      position: 'absolute',
      left: overlay.left,
      top: overlay.top,
      width: overlay.width,
      height: overlay.height,
      transform: `rotate(${overlay.rotation || 0}deg)`,
      transformOrigin: 'center center',
      zIndex: overlay.zIndex,
    }
  }, [overlay])

  return (
    <div style={style} data-overlay-id={overlay.id}>
      <SimpleLayerContent overlay={overlay} />
    </div>
  )
}
