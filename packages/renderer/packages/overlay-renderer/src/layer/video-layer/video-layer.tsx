import React, { useEffect, useMemo, useRef, useState } from 'react'
import { continueRender, delayRender, interpolate, useCurrentFrame, Video } from 'remotion'
import * as THREE from 'three'
import { ThreeCanvas, useVideoTexture } from '@remotion/three'
import { useFrame, useThree } from '@react-three/fiber'
import { RenderableOverlay, VideoOverlay } from '@app/shared/types/overlay'
import { useRenderContext } from '../render.context'
import { getProgressiveOverlayProps } from '../utils/getProgressiveOverlayProps'
import { VIDEO_PRELOAD_SECONDS } from '../constants'

import FragmentShader from '../glsl/main.frag'
import VertexShader from '../glsl/main.vert'

interface VideoLayerProps {
  overlay: RenderableOverlay & VideoOverlay
}

// 全屏 Three.js 视频平面组件
const VideoPlane: React.FC<{
  video: React.RefObject<HTMLVideoElement | null>
  overlay: VideoOverlay
}> = ({ video, overlay }) => {
  const vp = useThree(state => state.viewport)
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()

  const texture = useVideoTexture(video)
  const materialRef = useRef<THREE.ShaderMaterial>(null)

  // 配置纹理属性
  useMemo(() => {
    if (!texture) return
    texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  // 计算视频变换参数（包含坐标偏移）
  const { position, rotation } = useMemo(() => {
    // 旋转角度转换为弧度
    const rotationZ = -(overlay.rotation || 0) * Math.PI / 180

    // 以播放器左上为(0,0)，Three 以中心为(0,0)
    // 计算 Overlay 中心的像素坐标
    const centerX = (overlay.left ?? 0) + overlay.width / 2
    const centerY = (overlay.top ?? 0) + overlay.height / 2

    // 将像素坐标映射到 Three 世界坐标
    // X: 左->右，原点居中
    const finalX = (centerX - playerWidth / 2) / vp.factor
    // Y: 屏幕向下为正像素，Three 中向上为正，需要取反
    const finalY = (playerHeight / 2 - centerY) / vp.factor

    return {
      position: [finalX, finalY, 0] as [number, number, number],
      rotation: [0, 0, rotationZ] as [number, number, number],
    }
  }, [overlay.left, overlay.top, overlay.width, overlay.height, overlay.rotation, playerWidth, playerHeight, vp.factor])

  // 计算裁剪区域（归一化到纹理坐标，原点在左下）
  const cropVec = useMemo(() => {
    const c = overlay.cropData
    if (!c) return new THREE.Vector4(0, 0, 1, 1)
    const nx = c.x / 100.0
    const nyTop = c.y / 100.0
    const nw = c.width / 100.0
    const nh = c.height / 100.0
    const ny = 1.0 - (nyTop + nh)
    return new THREE.Vector4(nx, ny, nw, nh)
  }, [overlay.cropData])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_opacity: { value: 1 },
      u_crop: { value: cropVec },
      u_flip: { value: overlay.styles.flipX },
    }),
    [texture]
  )

  // 每帧更新透明度
  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.u_opacity.value = overlay.styles.opacity ?? 1
      materialRef.current.uniforms.u_crop.value = cropVec
      materialRef.current.uniforms.u_flip.value = overlay.styles.flipX
    }
  })

  return (
    <mesh
      position={position}
      rotation={rotation}
    >
      <planeGeometry args={[overlay.width / vp.factor, overlay.height / vp.factor]} />
      {texture && (
        <shaderMaterial
          ref={materialRef}
          vertexShader={VertexShader}
          fragmentShader={FragmentShader}
          uniforms={uniforms}
          transparent={true}
          toneMapped={false}
        />
      )}
    </mesh>
  )
}

const useVideoTransitionProgress = (overlay: VideoOverlay): {
  leadingProgress?: number;
  trailingProgress?: number;
  isPreloading: boolean
} => {
  const rawCurrentFrame = useCurrentFrame()
  const { playerMetadata: { fps } } = useRenderContext()

  const currentFrame = useMemo(() => {
    const preloadDurationFrames = VIDEO_PRELOAD_SECONDS * fps
    return Math.max(rawCurrentFrame - preloadDurationFrames, 0)
  }, [rawCurrentFrame, fps])

  const isPreloading = useMemo(() => rawCurrentFrame < VIDEO_PRELOAD_SECONDS * fps, [rawCurrentFrame, fps])

  const leadingProgress = useMemo(() => {
    const leadingTransition = overlay.leadingTransition
    if (!leadingTransition) return undefined

    const frameWhenMinProgress = 0
    const frameWhenMaxProgress = leadingTransition.durationInFrames

    if (currentFrame > frameWhenMaxProgress || currentFrame < frameWhenMinProgress) return undefined

    return interpolate(
      currentFrame,
      [frameWhenMinProgress, frameWhenMaxProgress],
      [0, 1],
    )
  }, [overlay.leadingTransition, currentFrame, fps])

  const trailingProgress = useMemo(() => {
    const trailingTransition = overlay.trailingTransition
    if (!trailingTransition) return undefined

    const leadingTransitionDuration = overlay.leadingTransition?.durationInFrames ?? 0

    const progressDuration = trailingTransition.durationInFrames
    const frameWhenMinProgress = leadingTransitionDuration / 2 + overlay.durationInFrames - progressDuration / 2
    const frameWhenMaxProgress = frameWhenMinProgress + progressDuration

    if (currentFrame < frameWhenMinProgress || currentFrame > frameWhenMaxProgress) return undefined

    return interpolate(
      currentFrame,
      [frameWhenMinProgress, frameWhenMaxProgress],
      [0, 1],
    )
  }, [overlay.trailingTransition, currentFrame, fps])

  return {
    isPreloading,
    leadingProgress,
    trailingProgress
  }
}

export const VideoLayer: React.FC<VideoLayerProps> = ({ overlay }) => {
  const src = overlay.localSrc || overlay.src
  // console.debug(`Using src=${src} for overlay ${overlay.id}`)

  const { isPreloading, leadingProgress, trailingProgress } = useVideoTransitionProgress(overlay)

  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()

  const videoRef = useRef<HTMLVideoElement | null>(null)

  const [videoData, setVideoData] = useState<any>(null)

  useEffect(() => {
    const handle = delayRender('Loading video')

    const handleLoadedMetadata = () => {
      setVideoData(true)
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      continueRender(handle)
    }

    videoRef.current?.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoRef.current?.addEventListener('error', handleError)

    return () => {
      videoRef.current?.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoRef.current?.removeEventListener('error', handleError)
      continueRender(handle)
    }
  }, [src])

  return (
    <div
      id={`video-layer-${overlay.id}`}
      style={{
        position: 'absolute',
        left: 0,
        top: 0,
        width: playerWidth,
        height: playerHeight,
        pointerEvents: 'none'
      }}
    >
      {videoData && (
        <ThreeCanvas
          width={playerWidth}
          height={playerHeight}
          camera={{ position: [0, 0, 1] }}
          style={{
            width: '100%',
            height: '100%',
            // border: '4px solid red',
            opacity: isPreloading ? 0
              : (
                (leadingProgress !== undefined ? leadingProgress : 1)
                * (trailingProgress !== undefined ? 1 - trailingProgress : 1)
              )
          }}
        >
          <VideoPlane video={videoRef} overlay={overlay} />
        </ThreeCanvas>
      )}

      {/* 隐藏的视频元素作为纹理源 */}
      <Video
        ref={videoRef}
        src={src}
        style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
        volume={overlay.styles.volume ?? 1}
        {...getProgressiveOverlayProps(overlay)}
      />
    </div>
  )
}
