import React, { useEffect, useMemo, useRef } from 'react'
import { TransitionOverlay, TransitionTypes, VideoOverlay } from '@app/shared/types/overlay'
import { useFrame, useThree } from '@react-three/fiber'
import { useRenderContext } from '../../render.context'
import { useOffthreadVideoTexture, useVideoTexture } from '@remotion/three'
import * as THREE from 'three'
import { Vector2 } from 'three'
import VertexShader from './glsl/main.vert'
import FragmentShader from './glsl/main.frag'
import { getRemotionEnvironment, interpolate } from 'remotion'
import { getProgressiveOverlayProps } from '../../utils/getProgressiveOverlayProps'

export type ProgressiveTransition = TransitionOverlay & {
  progress: number
}

type VideoPlaneProps = {
  videoRef: React.RefObject<HTMLVideoElement | null>
  overlay: VideoOverlay

  enterTransition?: ProgressiveTransition
  exitTransition?: ProgressiveTransition
}

const DEFAULT_CROP = new THREE.Vector4(0, 0, 1, 1)

function useSmartVideoTexture(overlay: VideoOverlay, videoRef: React.RefObject<HTMLVideoElement | null>) {
  const texture = (
    getRemotionEnvironment().isRendering
      ? useOffthreadVideoTexture({ src: overlay.src, ...getProgressiveOverlayProps(overlay) })
      : useVideoTexture(videoRef)
  )

  // 配置纹理属性
  useEffect(() => {
    if (!texture) return

    texture.wrapS = texture.wrapT = THREE.ClampToEdgeWrapping
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.needsUpdate = true
  }, [texture])

  return texture
}

function useVideoBasicInfo(overlay: VideoOverlay) {
  const vp = useThree(state => state.viewport)

  const { playerMetadata: { width: playerWidth, height: playerHeight } } = useRenderContext()

  // 计算视频变换参数（包含坐标偏移）
  const { position, rotation } = useMemo(() => {
    // 旋转角度转换为弧度
    const rotationZ = -(overlay.rotation || 0) * Math.PI / 180

    // 以播放器左上为(0,0)，Three 以中心为(0,0)
    // 计算 Overlay 中心的像素坐标
    const centerX = (overlay.left ?? 0) + overlay.width / 2
    const centerY = (overlay.top ?? 0) + overlay.height / 2

    // 将像素坐标映射到 Three 世界坐标
    // X: 左->右，原点居中
    const finalX = (centerX - playerWidth / 2) / vp.factor
    // Y: 屏幕向下为正像素，Three 中向上为正，需要取反
    const finalY = (playerHeight / 2 - centerY) / vp.factor

    return {
      position: [finalX, finalY, 0] as [number, number, number],
      rotation: [0, 0, rotationZ] as [number, number, number],
    }
  }, [overlay.left, overlay.top, overlay.width, overlay.height, overlay.rotation, playerWidth, playerHeight, vp.factor])

  // 计算裁剪区域（归一化到纹理坐标）
  const crop = useMemo(() => {
    const c = overlay.cropData
    if (!c) return DEFAULT_CROP
    const nx = c.x / 100.0
    const nyTop = c.y / 100.0
    const nw = c.width / 100.0
    const nh = c.height / 100.0
    const ny = 1.0 - (nyTop + nh)
    return new THREE.Vector4(nx, ny, nw, nh)
  }, [overlay.cropData])

  const size: [number, number] = [
    overlay.width / vp.factor,
    overlay.height / vp.factor
  ]

  return {
    position,
    rotation,
    crop,
    size
  }
}

function getUniformNameForTransitionType(type: TransitionTypes) {
  return `u_transition_${type}`
}

export const VideoPlane: React.FC<VideoPlaneProps> = ({ videoRef, overlay, enterTransition, exitTransition }) => {
  const texture = useSmartVideoTexture(overlay, videoRef)
  const { size, position, rotation, crop } = useVideoBasicInfo(overlay)

  const materialRef = useRef<THREE.ShaderMaterial>(null)

  const { effectProgress, transitionType } = useMemo(() => {
    if (enterTransition !== undefined) {
      const effectProgress = interpolate(
        enterTransition.progress,
        [0.5, 1],
        [1, 0],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      )

      return {
        effectProgress,
        transitionType: enterTransition.config.transitionType,
      }
    }

    if (exitTransition !== undefined) {
      const effectProgress = interpolate(
        exitTransition.progress,
        [0, 0.5],
        [0, 1],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      )

      return {
        effectProgress,
        transitionType: exitTransition.config.transitionType,
      }
    }

    return {}
  }, [overlay, enterTransition, exitTransition])

  const uniforms = useMemo(
    () => ({
      u_tex: { value: texture },
      u_resolution: { value: new Vector2(overlay.width, overlay.height) },
      u_opacity: { value: 1 },
      u_crop: { value: DEFAULT_CROP },
      u_flip: { value: false },
      u_progress: { value: 0 },
      ...(
        Object.fromEntries(
          Object.values(TransitionTypes)
            .map(type => [
              getUniformNameForTransitionType(type),
              { value: false }
            ])
        )
      )
    }),
    [texture, overlay.width, overlay.height]
  )

  const updateUniformValue = (uniformName: string, value: any) => {
    if (!materialRef.current) {
      return
    }

    const uniform = materialRef.current.uniforms[uniformName]
    if (!uniform) {
      console.warn('Uniform not found: ', uniformName)
      return
    }

    if (uniform.value !== value) {
      uniform.value = value
      // console.log(`[${overlay.id}] update uniform ${uniformName} to ${value}`)
    }
  }

  useFrame(() => {
    if (materialRef.current) {
      updateUniformValue('u_opacity', overlay.styles.opacity ?? 1)
      updateUniformValue('u_crop', crop)
      updateUniformValue('u_flip', overlay.styles.flipX)
      updateUniformValue('u_progress', effectProgress)

      for (const type of Object.values(TransitionTypes)) {
        const isTarget = type === transitionType

        const name = getUniformNameForTransitionType(type)
        updateUniformValue(name, isTarget)
      }
    }
  })

  return (
    <mesh position={position} rotation={rotation}>
      <planeGeometry args={size} />
      {texture && (
        <shaderMaterial
          ref={materialRef}
          transparent={true}
          toneMapped={false}
          vertexShader={VertexShader}
          fragmentShader={FragmentShader}
          uniforms={uniforms}
        />
      )}
    </mesh>
  )
}
