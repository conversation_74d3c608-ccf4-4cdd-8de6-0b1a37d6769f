// 快速高斯模糊, 基于 9-tap 高斯权重
vec4 blur9(sampler2D img, vec2 uv, vec2 resolution, float progress) {
  float radius = progress * 20.0;

  vec2 off1 = vec2(1.3846153846) * radius / resolution;
  vec2 off2 = vec2(3.2307692308) * radius / resolution;

  // 横向
  vec4 h = texture2D(img, uv) * 0.2270270270;
  h += texture2D(img, uv + vec2(off1.x, 0.0)) * 0.3162162162;
  h += texture2D(img, uv - vec2(off1.x, 0.0)) * 0.3162162162;
  h += texture2D(img, uv + vec2(off2.x, 0.0)) * 0.0702702703;
  h += texture2D(img, uv - vec2(off2.x, 0.0)) * 0.0702702703;

  // 纵向
  vec4 v = texture2D(img, uv) * 0.2270270270;
  v += texture2D(img, uv + vec2(0.0, off1.y)) * 0.3162162162;
  v += texture2D(img, uv - vec2(0.0, off1.y)) * 0.3162162162;
  v += texture2D(img, uv + vec2(0.0, off2.y)) * 0.0702702703;
  v += texture2D(img, uv - vec2(0.0, off2.y)) * 0.0702702703;

  return 0.5 * (h + v); // 合并横+纵
}
