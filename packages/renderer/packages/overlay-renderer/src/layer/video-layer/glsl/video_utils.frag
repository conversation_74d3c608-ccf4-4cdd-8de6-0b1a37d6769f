
vec2 applyVideoTransforms(vec2 uv, vec4 cropUV, bool flipX) {
  // 应用镜像翻转
  if (flipX) {
    uv.x = 1.0 - uv.x;
  }

  // 应用裁剪映射
  vec2 croppedUV = vec2(
  cropUV.x + uv.x * cropUV.z,
  cropUV.y + uv.y * cropUV.w
  );

  return croppedUV;
}

vec4 sampleVideoTexture(sampler2D tex, vec2 uv, vec4 cropUV, bool flipX) {
  vec2 transformedUV = applyVideoTransforms(uv, cropUV, flipX);

  // 边界检查
  if (transformedUV.x < 0.0 || transformedUV.x > 1.0 ||
  transformedUV.y < 0.0 || transformedUV.y > 1.0) {
    return vec4(0.0); // 透明
  }

  return texture2D(tex, transformedUV);
}
