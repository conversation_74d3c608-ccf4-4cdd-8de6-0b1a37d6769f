precision highp float;
varying vec2 vUv;

// 视频纹理
uniform sampler2D u_tex;

// 纹理分辨率（像素）
uniform vec2 u_resolution;

// 视频裁切区域. x, y, w, h
uniform vec4 u_crop;

// 可见度 (取值 0 ~ 1)
uniform float u_opacity;

// 是否(水平)翻转
uniform bool u_flip;

// 效果的进度, 取值 0 ~ 1
uniform float u_progress;

// 是否启用 "模糊" 转场效果
uniform bool u_transition_blur;

#include ./video_utils.frag
#include ./transition_blur.frag

void main() {
  vec4 color = sampleVideoTexture(u_tex, vUv, u_crop, u_flip);

  if (u_transition_blur == true) {
    color = blur9(u_tex, vUv, u_resolution, u_progress);
  }

  gl_FragColor = vec4(color.rgb, color.a * u_opacity);
}
