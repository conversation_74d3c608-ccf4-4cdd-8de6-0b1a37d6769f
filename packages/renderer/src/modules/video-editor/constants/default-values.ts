import { Overlay, OverlayType, TextOverlay, TransitionTypes } from '@app/shared/types/overlay'
import { FPS } from './basic-configs'
import { Track, TrackType } from '@/modules/video-editor/types'

// const OVERLAY_PLACEHOLDER: Omit<Overlay, 'type'> = {
//   id: 0,
//   left: 0,
//   top: 0,
//   width: 0,
//   height: 0,
//   from: 0,
//   durationInFrames: 0,
//   rotation: 0
// }

export const TEXT_DEFAULT_CLOUD_FONT_SRC = 'https://clipnest-library.oss-cn-shanghai.aliyuncs.com/resources/fonts/file_v2/6e4bee66c618418c9c8a23578e5bd468.ttf'
export const TEXT_DEFAULT_CLOUD_FONT_NAME = '阿里妈妈数黑体'

export const DEFAULT_TEXT_OVERLAY_STYLES: TextOverlay['styles'] = {
  fontSize: 150,
  fontWeight: 'normal',
  color: '#fff',
  backgroundColor: 'transparent',
  fontFamily: TEXT_DEFAULT_CLOUD_FONT_NAME,
  fontStyle: 'normal',
  underlineEnabled: false,
  lineSpacing: 0.2,
  textAlign: 'center',
  letterSpacing: 0,

  // 描边属性
  strokeEnabled: false,
  strokeWidth: 0,
  strokeColor: '#000000',

  // 阴影属性
  shadowEnabled: false,
  shadowDistance: 0,
  shadowAngle: 0,
  shadowBlur: 0,
  shadowColor: '#000000',
  shadowOpacity: 1,

  // 透明度
  textOpacity: 1,
  backgroundOpacity: '1',

  // 布局属性
  padding: 0.05, // 默认两侧各留5%空隙

  // 基础样式
  opacity: 1,
  zIndex: 1,
  transform: undefined
}

export const DEFAULT_OVERLAY: Pick<
  Overlay,
  'width' | 'height' | 'left' | 'top' | 'rotation' | 'durationInFrames'
> = {
  width: 0,
  height: 0,
  left: 0,
  top: 0,
  rotation: 0,
  durationInFrames: FPS * 3
} as const

export const DEFAULT_TEXT_OVERLAY: TextOverlay = {
  ...DEFAULT_OVERLAY,
  id: 0,
  from: 0,
  content: '默认文字',
  src: TEXT_DEFAULT_CLOUD_FONT_SRC,
  type: OverlayType.TEXT,
  styles: DEFAULT_TEXT_OVERLAY_STYLES
}

export const DEFAULT_TRACKS: Track[] = [
  {
    type: TrackType.STORYBOARD,
    overlays: [
      {
        id: 1,
        title: '',
        width: 0,
        height: 0,
        left: 0,
        top: 0,
        rotation: 0,
        durationInFrames: 90,
        type: OverlayType.STORYBOARD,
        from: 0,
      },
      {
        id: 2,
        title: '',
        width: 0,
        height: 0,
        left: 0,
        top: 0,
        rotation: 0,
        durationInFrames: 90,
        type: OverlayType.STORYBOARD,
        from: 90,
      },

      {
        ...DEFAULT_OVERLAY,
        type: OverlayType.TRANSITION,
        id: 999,
        from: 60,
        durationInFrames: 60,
        prevStoryboardIndex: 0,
        config: {
          transitionType: TransitionTypes.DISSOLVE,
        },
      }
    ]
  },

  {
    type: TrackType.VIDEO,
    isGlobalTrack: false,
    overlays: [
      {
        width: 1080,
        height: 1920,
        left: 0,
        top: 0,
        rotation: 60,
        durationInFrames: 90,
        id: 7,
        type: OverlayType.VIDEO,
        from: 90,
        originalDurationFrames: 121,
        originalMeta: {
          width: 1080,
          height: 1920,
          tileUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/2d2ec2e0d5b54644a7366b0389f98a22',
          coverUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/5a35736ce88c4cefb6a048ad54a3e64e'
        },
        content: '1.超好用拖把.mp4',
        src: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/533e233251c143b786b72fe9ec153c5b',
        styles: { volume: 0 },
        storyboardIndex: 1
      },
      {
        width: 1080,
        height: 1920,
        left: 0,
        top: 0,
        rotation: -60,
        durationInFrames: 90,
        id: 4,
        type: OverlayType.VIDEO,
        from: 0,
        originalDurationFrames: 142,
        originalMeta: {
          width: 1080,
          height: 1920,
          tileUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/83c112a204cb42309ccdb6f562621fdd',
          coverUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/581b38ddba8e418daee9be67ec625bd7'
        },
        content: '2月8日 (1)(10)-1.mp4',
        src: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/22cbbf1293d844a4b98d54d2eb443217',
        styles: { volume: 0 },
        storyboardIndex: 0
      }
    ]
  },
  {
    type: TrackType.VIDEO,
    isGlobalTrack: false,
    overlays: [
      {
        width: 1080,
        height: 1920,
        left: 0,
        top: 0,
        rotation: 45,
        durationInFrames: 90,
        id: 5,
        type: OverlayType.VIDEO,
        from: 90,
        originalDurationFrames: 142,
        originalMeta: {
          width: 1080,
          height: 1920,
          tileUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/83c112a204cb42309ccdb6f562621fdd',
          coverUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/581b38ddba8e418daee9be67ec625bd7'
        },
        content: '2月8日 (1)(10)-1.mp4',
        src: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/22cbbf1293d844a4b98d54d2eb443217',
        styles: { volume: 0 },
        storyboardIndex: 1
      },
      {
        width: 1080,
        height: 1920,
        left: 0,
        top: 0,
        rotation: -45,
        durationInFrames: 90,
        id: 6,
        type: OverlayType.VIDEO,
        from: 0,
        originalDurationFrames: 121,
        originalMeta: {
          width: 1080,
          height: 1920,
          tileUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/2d2ec2e0d5b54644a7366b0389f98a22',
          coverUrl: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/5a35736ce88c4cefb6a048ad54a3e64e'
        },
        content: '1.超好用拖把.mp4',
        src: 'http://47.99.131.55:48080/app-api/creative/oss/object-href/533e233251c143b786b72fe9ec153c5b',
        styles: { volume: 0 },
        storyboardIndex: 0
      }
    ]
  },
]
