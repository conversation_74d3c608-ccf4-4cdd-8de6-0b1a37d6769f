export enum OverlayType {
  STORYBOARD = 'storyboard',
  TEXT = 'text',
  VIDEO = 'video',
  SOUND = 'sound',
  STICKER = 'sticker',
  TRANSITION = 'transition',

  /**
   * @deprecated
   */
  CAPTION = 'caption',
}

// Base style properties
type BaseStyles = {
  opacity?: number
  zIndex?: number
  transform?: string
}

// Base animation type
type AnimationConfig = {
  enter?: string
  exit?: string
}

type TransitionConfig = {
  transitionType: TransitionTypes

  // 缓动函数
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out'

  // 用于可指定方向的转场类型
  direction?: 'left' | 'right' | 'up' | 'down'

  // 转场强度参数
  intensity?: number // 0-1，用于控制泛光强度、模糊程度等

  // 闪光颜色（仅用于 FLASH_WHITE/FLASH_BLACK）
  flashColor?: string

  // 缩放中心点（仅用于 ZOOM_IN/ZOOM_OUT）
  zoomCenter?: {
    x: number // 0-1
    y: number // 0-1
  }
}

/**
 * 用于视频/音频等可调节播放速度和裁切时长的 Overlay
 */
export type Progressive = {
  /**
   * 原始视频/音频的总帧数, 不受变速影响
   */
  originalDurationFrames: number

  /**
   * 播放速率, 默认为 1
   */
  speed?: number

  /**
   * 去片头的长度. 该帧数为原始帧数，不受变速影响
   */
  trimStartFrames?: number

  /**
   * 去片尾的长度. 该帧数为原始帧数，不受变速影响
   */
  trimEndFrames?: number
}

// Base overlay properties
export interface BaseOverlay {
  id: number
  type: OverlayType

  /**
   * 该 Overlay 在时间轴上显示的实际时长, 该值会随 `speed` 的变化而变化
   */
  durationInFrames: number
  from: number
  height: number
  left: number
  top: number
  width: number

  /**
   * 旋转角度
   */
  rotation: number

  /**
   * 标识该 Overlay 处于第几个分镜中
   */
  storyboardIndex?: number

  /**
   * 缓存到本地的资源路径
   */
  localSrc?: string
}

export interface TextOverlay extends BaseOverlay {
  type: OverlayType.TEXT
  content: string
  src: string
  styles: BaseStyles & {
    // 基础字体属性
    fontSize: number
    fontWeight: 'bold' | 'normal'
    color: string
    backgroundColor: string
    fontFamily: string
    fontStyle: 'italic' | 'normal'
    lineSpacing?: number
    letterSpacing?: number
    textAlign?: 'left' | 'center' | 'right'
    textShadowDistance?: string

    /**
     * 描边开关
     */
    strokeEnabled?: boolean
    /**
     * 描边宽度
     * @see {StyledTextResource.StyledTextContext.borderWidth}
     */
    strokeWidth?: number
    /**
     * 描边颜色
     * @see {StyledTextResource.StyledTextContext.borderColor}
     */
    strokeColor?: string

    /**
     * 阴影开关
     */
    shadowEnabled?: boolean
    /**
     * 阴影模糊度
     * @see {StyledTextResource.StyledTextContext.shadowBlur}
     */
    shadowBlur?: number
    /**
     * 阴影距离
     * @see {StyledTextResource.StyledTextContext.shadowDistance}
     */
    shadowDistance?: number
    /**
     * 阴影角度
     * @see {StyledTextResource.StyledTextContext.shadowAngle}
     */
    shadowAngle?: number
    /**
     * 阴影颜色
     * @see {StyledTextResource.StyledTextContext.shadowColor}
     */
    shadowColor?: string
    /**
     * 阴影可见度
     * @see {StyledTextResource.StyledTextContext.shadowColorAlpha}
     */
    shadowOpacity?: number

    /**
     * 文字可见度
     * @see {StyledTextResource.StyledTextContext.textAlpha}
     */
    textOpacity?: number

    // 下划线属性
    underlineEnabled?: boolean
    underlineWidth?: string // 下划线宽度
    underlineOffset?: string // 下划线偏移

    /**
     * 背景可见度
     * @see {StyledTextResource.StyledTextContext.backgroundAlpha}
     */
    backgroundOpacity?: string

    // 气泡图属性
    backgroundImage?: string // 气泡图背景图片URL
    bubbleTextRect?: [number, number, number, number] // 气泡图中文字的相对位置 [x, y, width, height]
    bubbleRatio?: number

    // 布局属性
    padding?: number
    border?: string
    animation?: AnimationConfig
  }
}

export interface VideoOverlay extends BaseOverlay, Progressive {
  type: OverlayType.VIDEO
  content: string
  src: string

  /**
   * 视频的原始元数据
   */
  originalMeta: {
    width: number
    height: number

    /**
     * 用于预览的视频关键帧的瓦片图 URL
     */
    tileUrl?: string

    /**
     * 视频封面图 URL
     */
    coverUrl?: string
  }

  /**
   * 视频裁剪相关属性
   * 单位均为百分比
   * @example `19.2` 代表 `19.2%`
   */
  cropData?: {
    x: number
    y: number
    width: number
    height: number
  }

  styles: BaseStyles & {
    flipX?: boolean
    objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
    objectPosition?: string
    volume?: number
    borderRadius?: string
    filter?: string
    boxShadow?: string
    border?: string
    paddingBackgroundColor?: string
    animation?: AnimationConfig // Using shared type

    /**
     * @deprecated
     */
    padding?: number
  }

  enterTransition?: TransitionOverlay
  exitTransition?: TransitionOverlay
}

export interface SoundOverlay extends BaseOverlay, Progressive {
  type: OverlayType.SOUND
  content: string
  src: string

  /**
   * 淡入时长（秒）
   * 用于视频、音频等媒体类型的淡入效果
   */
  fadeInDuration?: number

  /**
   * 淡出时长（秒）
   * 用于视频、音频等媒体类型的淡出效果
   */
  fadeOutDuration?: number

  styles: BaseStyles & {
    volume?: number
  }
}

type CaptionWord = {
  word: string
  startMs: number
  endMs: number
  confidence: number
}

export type Caption = {
  text: string
  startMs: number
  endMs: number
  timestampMs: number | null
  confidence: number | null
  words: CaptionWord[]
}

// Update CaptionOverlay to include styling for highlighted words
export interface CaptionStyles {
  fontFamily: string
  fontSize: string
  lineHeight: number
  textAlign: 'left' | 'center' | 'right'
  color: string
  backgroundColor?: string
  background?: string
  backdropFilter?: string
  padding?: string
  fontWeight?: number | string
  letterSpacing?: string
  textShadow?: string
  borderRadius?: string
  transition?: string
  highlightStyle?: {
    backgroundColor?: string
    color?: string
    scale?: number
    fontWeight?: number
    textShadow?: string
    padding?: string
    borderRadius?: string
    transition?: string
    background?: string
    border?: string
    backdropFilter?: string
  }
}

export interface CaptionOverlay extends BaseOverlay {
  type: OverlayType.CAPTION
  captions: Caption[]
  styles?: CaptionStyles
  template?: string
}

// Sticker overlay specific
export interface StickerOverlay extends BaseOverlay {
  type: OverlayType.STICKER
  content: string
  src: string
  styles: BaseStyles & {
    fill?: string
    stroke?: string
    strokeWidth?: number
    scale?: number
    filter?: string
    animation?: AnimationConfig

    borderRadius?: string
    objectPosition?: string
    boxShadow?: string
    border?: string
    padding?: number
    paddingBackgroundColor?: string
  }
}

export interface StoryboardOverlay extends BaseOverlay {
  type: OverlayType.STORYBOARD
  title: string
}

/**
 * NOTE: 该枚举的值与 glsl 中的宏定义名保持一致
 */
export enum TransitionTypes {
  /** 叠化 */
  DISSOLVE = 'dissolve',

  /** (向某个方向)滑动 */
  SLIDE = 'slide',

  /** (向某个方向)擦除 */
  WIPE = 'wipe',

  /** (向某个方向)撕扯 */
  TEAR = 'tear',

  /** 泛光 */
  BLOOM = 'bloom',

  /** 模糊 */
  BLUR = 'blur',

  /** 闪光 */
  FLASH = 'flash',

  /** 推远 */
  ZOOM_OUT = 'zoom_out',

  /** 推近 */
  ZOOM_IN = 'zoom_in',
}

export interface TransitionOverlay extends BaseOverlay {
  type: OverlayType.TRANSITION

  prevStoryboardIndex: number
  // from: never

  // 转场配置
  config: TransitionConfig

  // 关联的两个视频 overlay ID
  fromVideoId?: number
  toVideoId?: number
}

export type Overlay =
  | TextOverlay
  | VideoOverlay
  | SoundOverlay
  | CaptionOverlay
  | StickerOverlay
  | StoryboardOverlay
  | TransitionOverlay

export type RenderableOverlay = Overlay & {
  zIndex: number
}
